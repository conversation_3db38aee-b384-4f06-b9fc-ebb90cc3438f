package com.tfkcolin.joceladmin.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.tfkcolin.joceladmin.ui.screens.auth.LoginScreen
import com.tfkcolin.joceladmin.ui.screens.dashboard.DashboardScreen
import com.tfkcolin.joceladmin.ui.screens.command.CommandScreen
import com.tfkcolin.joceladmin.ui.screens.command.CommandDetailsScreen
import com.tfkcolin.joceladmin.ui.screens.cargo.CargoScreen
import com.tfkcolin.joceladmin.ui.screens.cargo.CargoDetailsScreen
import com.tfkcolin.joceladmin.ui.screens.financial.FinancialDashboardScreen
import com.tfkcolin.joceladmin.ui.screens.category.CategoryListScreen
import com.tfkcolin.joceladmin.ui.screens.category.CategoryDetailsScreen
import com.tfkcolin.joceladmin.ui.screens.product.ProductCatalogScreen
import com.tfkcolin.joceladmin.ui.screens.product.ProductDetailsScreen

/**
 * Main navigation component for JocelAdmin app
 * Implements navigation structure based on the app architecture
 */
@Composable
fun JocelAdminNavigation(
    navController: NavHostController = rememberNavController(),
    startDestination: String = JocelAdminDestinations.LOGIN
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Authentication Flow
        composable(JocelAdminDestinations.LOGIN) {
            LoginScreen(
                onNavigateToSignUp = {
                    navController.navigate(JocelAdminDestinations.SIGN_UP)
                },
                onNavigateToDashboard = {
                    navController.navigate(JocelAdminDestinations.DASHBOARD) {
                        popUpTo(JocelAdminDestinations.LOGIN) { inclusive = true }
                    }
                }
            )
        }

        composable(JocelAdminDestinations.SIGN_UP) {
            // SignUpScreen will be implemented in Phase 2
            // SignUpScreen(
            //     onNavigateToLogin = {
            //         navController.popBackStack()
            //     },
            //     onNavigateToDashboard = {
            //         navController.navigate(JocelAdminDestinations.DASHBOARD) {
            //             popUpTo(JocelAdminDestinations.LOGIN) { inclusive = true }
            //         }
            //     }
            // )
        }

        // Main App Flow
        composable(JocelAdminDestinations.DASHBOARD) {
            DashboardScreen(
                onNavigateToCommands = {
                    navController.navigate(JocelAdminDestinations.COMMANDS)
                },
                onNavigateToCargo = {
                    navController.navigate(JocelAdminDestinations.CARGO)
                },
                onNavigateToFinancial = {
                    navController.navigate(JocelAdminDestinations.FINANCIAL)
                },
                onNavigateToProducts = {
                    navController.navigate(JocelAdminDestinations.PRODUCTS)
                },
                onNavigateToUsers = {
                    navController.navigate(JocelAdminDestinations.USERS)
                },
                onSignOut = {
                    navController.navigate(JocelAdminDestinations.LOGIN) {
                        popUpTo(0) { inclusive = true }
                    }
                }
            )
        }

        // Command Management
        composable(JocelAdminDestinations.COMMANDS) {
            CommandScreen(
                onNavigateToDetails = { commandId ->
                    navController.navigate("${JocelAdminDestinations.COMMAND_DETAILS}/$commandId")
                },
                onNavigateToCreate = {
                    navController.navigate("${JocelAdminDestinations.COMMAND_DETAILS}/new")
                }
            )
        }

        composable("${JocelAdminDestinations.COMMAND_DETAILS}/{commandId}") { backStackEntry ->
            val commandId = backStackEntry.arguments?.getString("commandId") ?: ""
            CommandDetailsScreen(
                commandId = if (commandId == "new") "" else commandId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Cargo Management
        composable(JocelAdminDestinations.CARGO) {
            CargoScreen(
                onNavigateToDetails = { cargoId ->
                    navController.navigate("${JocelAdminDestinations.CARGO_DETAILS}/$cargoId")
                },
                onNavigateToCreate = {
                    navController.navigate("${JocelAdminDestinations.CARGO_DETAILS}/new")
                }
            )
        }

        composable("${JocelAdminDestinations.CARGO_DETAILS}/{cargoId}") { backStackEntry ->
            val cargoId = backStackEntry.arguments?.getString("cargoId") ?: ""
            CargoDetailsScreen(
                cargoId = if (cargoId == "new") "" else cargoId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Financial Management
        composable(JocelAdminDestinations.FINANCIAL) {
            FinancialDashboardScreen(
                onNavigateToTransactions = {
                    // TODO: Navigate to transactions list screen
                },
                onNavigateToCountries = {
                    // TODO: Navigate to countries management screen
                }
            )
        }

        composable(JocelAdminDestinations.TRANSACTIONS) {
            // TransactionListScreen will be implemented in Phase 3
        }

        // Product Catalog
        composable(JocelAdminDestinations.PRODUCTS) {
            ProductCatalogScreen(
                onNavigateToDetails = { productId ->
                    navController.navigate("${JocelAdminDestinations.PRODUCT_DETAILS}/$productId")
                },
                onNavigateToCreate = {
                    navController.navigate("${JocelAdminDestinations.PRODUCT_DETAILS}/new")
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable("${JocelAdminDestinations.PRODUCT_DETAILS}/{productId}") { backStackEntry ->
            val productId = backStackEntry.arguments?.getString("productId") ?: ""
            ProductDetailsScreen(
                productId = if (productId == "new") "" else productId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToImageGallery = {
                    navController.navigate(JocelAdminDestinations.IMAGE_GALLERY)
                }
            )
        }

        // Category Management
        composable(JocelAdminDestinations.CATEGORIES) {
            CategoryListScreen(
                onNavigateToDetails = { categoryId ->
                    navController.navigate("${JocelAdminDestinations.CATEGORY_DETAILS}/$categoryId")
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable("${JocelAdminDestinations.CATEGORY_DETAILS}/{${JocelAdminArguments.CATEGORY_ID}}") { backStackEntry ->
            val categoryId = backStackEntry.arguments?.getString(JocelAdminArguments.CATEGORY_ID) ?: ""
            CategoryDetailsScreen(
                categoryId = if (categoryId == "new") "" else categoryId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToGenres = { categoryId ->
                    navController.navigate("${JocelAdminDestinations.GENRES}/$categoryId")
                }
            )
        }

        // User Management
        composable(JocelAdminDestinations.USERS) {
            // UserManagementScreen will be implemented in Phase 3
        }

        // Settings
        composable(JocelAdminDestinations.SETTINGS) {
            // SettingsScreen will be implemented in Phase 4
        }

        // Profile
        composable(JocelAdminDestinations.PROFILE) {
            // ProfileScreen will be implemented in Phase 4
        }
    }
}

/**
 * Navigation destinations for the app
 */
object JocelAdminDestinations {
    // Authentication
    const val LOGIN = "login"
    const val SIGN_UP = "sign_up"
    const val FORGOT_PASSWORD = "forgot_password"

    // Main App
    const val DASHBOARD = "dashboard"

    // Command Management
    const val COMMANDS = "commands"
    const val COMMAND_DETAILS = "command_details"
    const val CREATE_COMMAND = "create_command"
    const val EDIT_COMMAND = "edit_command"

    // Cargo Management
    const val CARGO = "cargo"
    const val CARGO_DETAILS = "cargo_details"
    const val CREATE_CARGO = "create_cargo"
    const val EDIT_CARGO = "edit_cargo"
    const val SHIPMENTS = "shipments"
    const val SHIPMENT_DETAILS = "shipment_details"

    // Financial Management
    const val FINANCIAL = "financial"
    const val TRANSACTIONS = "transactions"
    const val TRANSACTION_DETAILS = "transaction_details"
    const val CREATE_TRANSACTION = "create_transaction"
    const val FINANCIAL_REPORTS = "financial_reports"

    // Product Catalog
    const val PRODUCTS = "products"
    const val PRODUCT_DETAILS = "product_details"
    const val UPLOAD_IMAGES = "upload_images"
    const val IMAGE_GALLERY = "image_gallery"

    // Category Management
    const val CATEGORIES = "categories"
    const val CATEGORY_DETAILS = "category_details"
    const val GENRES = "genres"
    const val GENRE_DETAILS = "genre_details"

    // User Management
    const val USERS = "users"
    const val USER_DETAILS = "user_details"
    const val CREATE_USER = "create_user"
    const val EDIT_USER = "edit_user"

    // Settings & Profile
    const val SETTINGS = "settings"
    const val PROFILE = "profile"
    const val ABOUT = "about"
}

/**
 * Navigation arguments
 */
object JocelAdminArguments {
    const val COMMAND_ID = "commandId"
    const val CARGO_ID = "cargoId"
    const val SHIPMENT_ID = "shipmentId"
    const val TRANSACTION_ID = "transactionId"
    const val USER_ID = "userId"
    const val IMAGE_ID = "imageId"
    const val CATEGORY_ID = "categoryId"
    const val GENRE_ID = "genreId"
}
