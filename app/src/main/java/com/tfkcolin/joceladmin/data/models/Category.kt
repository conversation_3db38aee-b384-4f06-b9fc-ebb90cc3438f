package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Category entity for hierarchical organization
 */
@Serializable
data class Category(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val slug: String = "",
    val parentId: String? = null,
    val level: Int = 0, // 0 = root, 1 = sub-category, etc.
    val path: String = "", // e.g., "electronics/phones/smartphones"
    val iconUrl: String = "",
    val bannerUrl: String = "",
    val isActive: Boolean = true,
    val isVisible: Boolean = true,
    val sortOrder: Int = 0,
    val productCount: Int = 0,
    val imageCount: Int = 0,
    val subcategoryCount: Int = 0,
    val seoData: CategorySEO = CategorySEO(),
    val metadata: CategoryMetadata = CategoryMetadata(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val updatedBy: String = "",
    val children: List<Category> = emptyList(), // For hierarchical display
    val genres: List<Genre> = emptyList()
)

/**
 * Genre entity for sub-categorization within categories
 */
@Serializable
data class Genre(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val slug: String = "",
    val categoryId: String = "",
    val iconUrl: String = "",
    val isActive: Boolean = true,
    val isVisible: Boolean = true,
    val sortOrder: Int = 0,
    val productCount: Int = 0,
    val imageCount: Int = 0,
    val tags: List<String> = emptyList(),
    val seoData: GenreSEO = GenreSEO(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val updatedBy: String = ""
)

/**
 * Category SEO data
 */
@Serializable
data class CategorySEO(
    val metaTitle: String = "",
    val metaDescription: String = "",
    val keywords: List<String> = emptyList(),
    val canonicalUrl: String = "",
    val ogTitle: String = "",
    val ogDescription: String = "",
    val ogImage: String = ""
)

/**
 * Genre SEO data
 */
@Serializable
data class GenreSEO(
    val metaTitle: String = "",
    val metaDescription: String = "",
    val keywords: List<String> = emptyList(),
    val canonicalUrl: String = ""
)

/**
 * Category metadata for additional information
 */
@Serializable
data class CategoryMetadata(
    val attributes: Map<String, String> = emptyMap(),
    val filters: List<CategoryFilter> = emptyList(),
    val displaySettings: CategoryDisplaySettings = CategoryDisplaySettings(),
    val businessRules: CategoryBusinessRules = CategoryBusinessRules()
)

/**
 * Category filter configuration
 */
@Serializable
data class CategoryFilter(
    val id: String = "",
    val name: String = "",
    val type: FilterType = FilterType.TEXT,
    val options: List<String> = emptyList(),
    val isRequired: Boolean = false,
    val isMultiSelect: Boolean = false,
    val sortOrder: Int = 0
)

/**
 * Filter types for category filters
 */
@Serializable
enum class FilterType {
    TEXT,
    NUMBER,
    RANGE,
    SELECT,
    MULTI_SELECT,
    BOOLEAN,
    DATE,
    COLOR,
    SIZE
}

/**
 * Category display settings
 */
@Serializable
data class CategoryDisplaySettings(
    val layout: CategoryLayout = CategoryLayout.GRID,
    val itemsPerPage: Int = 20,
    val showSubcategories: Boolean = true,
    val showFilters: Boolean = true,
    val showSorting: Boolean = true,
    val defaultSortBy: ProductSortBy = ProductSortBy.NAME,
    val defaultSortOrder: SortOrder = SortOrder.ASC
)

/**
 * Category layout options
 */
@Serializable
enum class CategoryLayout {
    GRID,
    LIST,
    MASONRY,
    CAROUSEL
}

/**
 * Category business rules
 */
@Serializable
data class CategoryBusinessRules(
    val allowProducts: Boolean = true,
    val allowSubcategories: Boolean = true,
    val maxSubcategoryLevels: Int = 3,
    val requireApproval: Boolean = false,
    val autoAssignGenres: Boolean = false,
    val inheritParentSettings: Boolean = true
)

/**
 * Category tree node for hierarchical display
 */
@Serializable
data class CategoryTreeNode(
    val category: Category,
    val children: List<CategoryTreeNode> = emptyList(),
    val isExpanded: Boolean = false,
    val isSelected: Boolean = false,
    val level: Int = 0
)

/**
 * Category search filters
 */
@Serializable
data class CategorySearchFilters(
    val parentId: String? = null,
    val level: Int? = null,
    val isActive: Boolean? = null,
    val isVisible: Boolean? = null,
    val hasProducts: Boolean? = null,
    val hasImages: Boolean? = null,
    val searchQuery: String = "",
    val sortBy: CategorySortBy = CategorySortBy.NAME,
    val sortOrder: SortOrder = SortOrder.ASC
)

/**
 * Category sorting options
 */
@Serializable
enum class CategorySortBy {
    NAME,
    SORT_ORDER,
    PRODUCT_COUNT,
    IMAGE_COUNT,
    CREATED_DATE,
    UPDATED_DATE
}

/**
 * Category creation request
 */
@Serializable
data class CategoryCreateRequest(
    val name: String,
    val description: String = "",
    val slug: String = "",
    val parentId: String? = null,
    val iconUrl: String = "",
    val bannerUrl: String = "",
    val sortOrder: Int = 0,
    val seoData: CategorySEO = CategorySEO(),
    val metadata: CategoryMetadata = CategoryMetadata()
)

/**
 * Category update request
 */
@Serializable
data class CategoryUpdateRequest(
    val id: String,
    val name: String? = null,
    val description: String? = null,
    val slug: String? = null,
    val parentId: String? = null,
    val iconUrl: String? = null,
    val bannerUrl: String? = null,
    val isActive: Boolean? = null,
    val isVisible: Boolean? = null,
    val sortOrder: Int? = null,
    val seoData: CategorySEO? = null,
    val metadata: CategoryMetadata? = null
)

/**
 * Genre creation request
 */
@Serializable
data class GenreCreateRequest(
    val name: String,
    val description: String = "",
    val slug: String = "",
    val categoryId: String,
    val iconUrl: String = "",
    val sortOrder: Int = 0,
    val tags: List<String> = emptyList(),
    val seoData: GenreSEO = GenreSEO()
)

/**
 * Genre update request
 */
@Serializable
data class GenreUpdateRequest(
    val id: String,
    val name: String? = null,
    val description: String? = null,
    val slug: String? = null,
    val categoryId: String? = null,
    val iconUrl: String? = null,
    val isActive: Boolean? = null,
    val isVisible: Boolean? = null,
    val sortOrder: Int? = null,
    val tags: List<String>? = null,
    val seoData: GenreSEO? = null
)

/**
 * Category statistics
 */
@Serializable
data class CategoryStats(
    val totalCategories: Int = 0,
    val activeCategories: Int = 0,
    val visibleCategories: Int = 0,
    val rootCategories: Int = 0,
    val maxDepth: Int = 0,
    val categoriesWithProducts: Int = 0,
    val categoriesWithImages: Int = 0,
    val averageProductsPerCategory: Double = 0.0,
    val averageImagesPerCategory: Double = 0.0,
    val topCategories: List<CategorySummary> = emptyList(),
    val categoryHierarchy: List<CategoryTreeNode> = emptyList()
)

/**
 * Category summary for lists and analytics
 */
@Serializable
data class CategorySummary(
    val id: String,
    val name: String,
    val slug: String,
    val level: Int,
    val productCount: Int,
    val imageCount: Int,
    val isActive: Boolean,
    val isVisible: Boolean,
    val iconUrl: String = "",
    val parentName: String = ""
)
