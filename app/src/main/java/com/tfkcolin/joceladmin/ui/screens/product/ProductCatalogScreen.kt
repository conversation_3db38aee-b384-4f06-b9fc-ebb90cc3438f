package com.tfkcolin.joceladmin.ui.screens.product

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.paging.compose.LazyPagingItems
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import com.tfkcolin.joceladmin.data.models.Product
import com.tfkcolin.joceladmin.ui.components.common.JACTopAppBar
import com.tfkcolin.joceladmin.ui.viewmodels.ProductCatalogViewModel
import com.tfkcolin.joceladmin.ui.viewmodels.ViewMode

/**
 * Main product catalog screen with search and filtering
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductCatalogScreen(
    onNavigateToDetails: (String) -> Unit,
    onNavigateToCreate: () -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: ProductCatalogViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val searchFilters by viewModel.searchFilters.collectAsState()
    val products = viewModel.products.collectAsLazyPagingItems()
    
    var showFilterPanel by remember { mutableStateOf(false) }
    
    // Handle error display
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show snackbar or handle error
            viewModel.clearError()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        JACTopAppBar(
            title = "Product Catalog",
            onNavigationClick = onNavigateBack,
            actions = {
                // View mode toggle
                IconButton(
                    onClick = { viewModel.toggleViewMode() }
                ) {
                    Icon(
                        imageVector = if (uiState.viewMode == ViewMode.GRID) 
                            Icons.Default.ViewList 
                        else 
                            Icons.Default.GridView,
                        contentDescription = "Toggle view mode"
                    )
                }
                
                // Selection mode toggle
                IconButton(
                    onClick = { viewModel.toggleSelectionMode() }
                ) {
                    Icon(
                        imageVector = if (uiState.isSelectionMode) 
                            Icons.Default.Close 
                        else 
                            Icons.Default.SelectAll,
                        contentDescription = "Toggle selection mode"
                    )
                }
                
                // Add product
                IconButton(
                    onClick = onNavigateToCreate
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add product"
                    )
                }
            }
        )

        // Search Bar
        ProductSearchBar(
            searchQuery = searchFilters.searchQuery ?: "",
            onSearchQueryChange = viewModel::updateSearchQuery,
            onSearchSubmit = { /* Search is automatic */ },
            onFilterClick = { showFilterPanel = !showFilterPanel },
            hasActiveFilters = hasActiveFilters(searchFilters),
            modifier = Modifier.padding(16.dp)
        )

        // Quick Filter Chips
        if (uiState.categories.isNotEmpty()) {
            QuickFilterChips(
                categories = uiState.categories,
                selectedCategory = searchFilters.categories.firstOrNull(),
                onCategorySelect = viewModel::updateCategoryFilter,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        // Active Filters Display
        ActiveFiltersDisplay(
            filters = searchFilters,
            onRemoveFilter = { filterKey ->
                when (filterKey) {
                    "category" -> viewModel.updateCategoryFilter(null)
                    "genre" -> viewModel.updateGenreFilter(null)
                    "brand" -> viewModel.updateBrandFilter(null)
                    "available" -> viewModel.updateAvailabilityFilter(null)
                    "featured" -> viewModel.updateFeaturedFilter(null)
                    "price" -> {
                        // Clear price range - would need to implement this
                    }
                }
            },
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Advanced Filter Panel
        if (showFilterPanel) {
            AdvancedFilterPanel(
                filters = searchFilters,
                categories = uiState.categories,
                onFiltersChange = { newFilters ->
                    // Apply all filter changes
                    newFilters.categories.firstOrNull()?.let(viewModel::updateCategoryFilter)
                    newFilters.genres.firstOrNull()?.let(viewModel::updateGenreFilter)
                    newFilters.brands.firstOrNull()?.let(viewModel::updateBrandFilter)
                    newFilters.isAvailable?.let(viewModel::updateAvailabilityFilter)
                    newFilters.isFeatured?.let(viewModel::updateFeaturedFilter)
                },
                onClearFilters = {
                    viewModel.clearFilters()
                    showFilterPanel = false
                },
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )
        }

        // Product Statistics
        uiState.productStats?.let { stats ->
            ProductStatsOverview(
                stats = stats,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )
        }

        // Selection Mode Actions
        if (uiState.isSelectionMode && uiState.selectedProducts.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${uiState.selectedProducts.size} selected",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    Row {
                        TextButton(
                            onClick = { viewModel.clearSelection() }
                        ) {
                            Text("Clear")
                        }
                        
                        TextButton(
                            onClick = { viewModel.deleteSelectedProducts() },
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("Delete")
                        }
                    }
                }
            }
        }

        // Products List/Grid
        when (uiState.viewMode) {
            ViewMode.GRID -> {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(
                        count = products.itemCount,
                        key = { index -> products[index]?.id ?: index }
                    ) { index ->
                        products[index]?.let { product ->
                            ProductGridCard(
                                product = product,
                                isSelected = uiState.selectedProducts.contains(product.id),
                                isSelectionMode = uiState.isSelectionMode,
                                onProductClick = { prod -> onNavigateToDetails(prod.id) },
                                onSelectionToggle = viewModel::toggleProductSelection
                            )
                        }
                    }
                    
                    // Loading state
                    when (products.loadState.append) {
                        is LoadState.Loading -> {
                            item {
                                Box(
                                    modifier = Modifier.fillMaxWidth(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator()
                                }
                            }
                        }
                        is LoadState.Error -> {
                            item {
                                Text(
                                    text = "Error loading products",
                                    color = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.padding(16.dp)
                                )
                            }
                        }
                        else -> {}
                    }
                }
            }
            
            ViewMode.LIST -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        count = products.itemCount,
                        key = { index -> products[index]?.id ?: index }
                    ) { index ->
                        products[index]?.let { product ->
                            ProductListItem(
                                product = product,
                                isSelected = uiState.selectedProducts.contains(product.id),
                                isSelectionMode = uiState.isSelectionMode,
                                onProductClick = { prod -> onNavigateToDetails(prod.id) },
                                onSelectionToggle = viewModel::toggleProductSelection
                            )
                        }
                    }
                    
                    // Loading state
                    when (products.loadState.append) {
                        is LoadState.Loading -> {
                            item {
                                Box(
                                    modifier = Modifier.fillMaxWidth(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator()
                                }
                            }
                        }
                        is LoadState.Error -> {
                            item {
                                Text(
                                    text = "Error loading products",
                                    color = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.padding(16.dp)
                                )
                            }
                        }
                        else -> {}
                    }
                }
            }
        }
    }
}

/**
 * Check if there are active filters
 */
private fun hasActiveFilters(filters: com.tfkcolin.joceladmin.data.models.ProductSearchFilters): Boolean {
    return filters.categories.isNotEmpty() ||
            filters.genres.isNotEmpty() ||
            filters.brands.isNotEmpty() ||
            filters.isAvailable != null ||
            filters.isFeatured != null ||
            filters.priceRange != null ||
            filters.stockRange != null ||
            filters.tags.isNotEmpty()
}
