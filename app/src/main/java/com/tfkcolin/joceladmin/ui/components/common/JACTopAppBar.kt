package com.tfkcolin.joceladmin.ui.components.common

import androidx.compose.foundation.layout.RowScope
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * Custom top app bar component for JocelAdmin
 * Provides consistent navigation and action patterns across screens
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACTopAppBar(
    title: String,
    onNavigationClick: (() -> Unit)? = null,
    navigationIcon: ImageVector = Icons.Default.ArrowBack,
    navigationContentDescription: String = "Navigate back",
    actions: @Composable RowScope.() -> Unit = {},
    colors: TopAppBarColors = TopAppBarDefaults.topAppBarColors()
) {
    TopAppBar(
        title = { Text(title) },
        navigationIcon = {
            onNavigationClick?.let { onClick ->
                IconButton(onClick = onClick) {
                    Icon(
                        imageVector = navigationIcon,
                        contentDescription = navigationContentDescription
                    )
                }
            }
        },
        actions = actions,
        colors = colors
    )
}
