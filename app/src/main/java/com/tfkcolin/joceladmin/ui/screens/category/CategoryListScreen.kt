package com.tfkcolin.joceladmin.ui.screens.category

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.collectAsLazyPagingItems
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.CategorySearchFilters
import com.tfkcolin.joceladmin.ui.components.common.JACTopAppBar
import com.tfkcolin.joceladmin.ui.viewmodels.CategoryListViewModel

/**
 * Screen for managing categories
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryListScreen(
    onNavigateToDetails: (String) -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: CategoryListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val searchFilters by viewModel.searchFilters.collectAsStateWithLifecycle()
    val categories = viewModel.categories.collectAsLazyPagingItems()
    
    var showFilters by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showStatusDialog by remember { mutableStateOf(false) }
    var statusDialogActivate by remember { mutableStateOf(true) }

    // Handle messages
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // Show error snackbar
            viewModel.clearError()
        }
    }

    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // Show success snackbar
            viewModel.clearMessage()
        }
    }

    Scaffold(
        topBar = {
            JACTopAppBar(
                title = "Categories",
                onNavigationClick = onNavigateBack,
                actions = {
                    // Search toggle
                    IconButton(onClick = { showFilters = !showFilters }) {
                        Icon(Icons.Default.Search, contentDescription = "Search")
                    }
                    
                    // Selection mode toggle
                    IconButton(onClick = viewModel::toggleSelectionMode) {
                        Icon(
                            if (uiState.isSelectionMode) Icons.Default.Close else Icons.Default.CheckCircle,
                            contentDescription = if (uiState.isSelectionMode) "Exit Selection" else "Select Items"
                        )
                    }
                    
                    // More options
                    var showMenu by remember { mutableStateOf(false) }
                    Box {
                        IconButton(onClick = { showMenu = true }) {
                            Icon(Icons.Default.MoreVert, contentDescription = "More Options")
                        }
                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Refresh") },
                                onClick = {
                                    showMenu = false
                                    viewModel.refresh()
                                },
                                leadingIcon = { Icon(Icons.Default.Refresh, contentDescription = null) }
                            )
                            DropdownMenuItem(
                                text = { Text("View Hierarchy") },
                                onClick = {
                                    showMenu = false
                                    // TODO: Switch to hierarchy view
                                },
                                leadingIcon = { Icon(Icons.Default.AccountTree, contentDescription = null) }
                            )
                        }
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { onNavigateToDetails("") }
            ) {
                Icon(Icons.Default.Add, contentDescription = "Add Category")
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Statistics Card
            val categoryStats = uiState.categoryStats
            if (categoryStats != null) {
                CategoryStatsCard(
                    stats = categoryStats,
                    modifier = Modifier.padding(16.dp)
                )
            }

            // Filters Panel
            if (showFilters) {
                CategoryFilterPanel(
                    filters = searchFilters,
                    onFiltersChange = viewModel::updateFilters,
                    onClearFilters = { 
                        viewModel.updateFilters(CategorySearchFilters())
                    },
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            // Selection Actions
            if (uiState.isSelectionMode && uiState.selectedCategories.isNotEmpty()) {
                SelectionActionsBar(
                    selectedCount = uiState.selectedCategories.size,
                    onActivate = {
                        statusDialogActivate = true
                        showStatusDialog = true
                    },
                    onDeactivate = {
                        statusDialogActivate = false
                        showStatusDialog = true
                    },
                    onDelete = { showDeleteDialog = true },
                    onClearSelection = viewModel::clearSelections,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            // Categories List
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(
                    count = categories.itemCount,
                    key = { index -> categories[index]?.id ?: index }
                ) { index ->
                    val category = categories[index]
                    category?.let {
                        CategoryListItem(
                            category = it,
                            isSelected = uiState.selectedCategories.contains(it.id),
                            isSelectionMode = uiState.isSelectionMode,
                            onClick = {
                                if (uiState.isSelectionMode) {
                                    viewModel.selectCategory(it.id)
                                } else {
                                    onNavigateToDetails(it.id)
                                }
                            },
                            onLongClick = {
                                if (!uiState.isSelectionMode) {
                                    viewModel.toggleSelectionMode()
                                }
                                viewModel.selectCategory(it.id)
                            }
                        )
                    }
                }
            }
        }
    }

    // Delete Confirmation Dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("Delete Categories") },
            text = { 
                Text("Are you sure you want to delete ${uiState.selectedCategories.size} selected categories? This action cannot be undone.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        viewModel.deleteSelectedCategories()
                    }
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Status Change Dialog
    if (showStatusDialog) {
        AlertDialog(
            onDismissRequest = { showStatusDialog = false },
            title = { 
                Text(if (statusDialogActivate) "Activate Categories" else "Deactivate Categories")
            },
            text = { 
                val action = if (statusDialogActivate) "activate" else "deactivate"
                Text("Are you sure you want to $action ${uiState.selectedCategories.size} selected categories?")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showStatusDialog = false
                        viewModel.toggleSelectedCategoriesStatus(statusDialogActivate)
                    }
                ) {
                    Text(if (statusDialogActivate) "Activate" else "Deactivate")
                }
            },
            dismissButton = {
                TextButton(onClick = { showStatusDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun CategoryStatsCard(
    stats: com.tfkcolin.joceladmin.data.models.CategoryStats,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem(
                label = "Total",
                value = stats.totalCategories.toString()
            )
            StatItem(
                label = "Active",
                value = stats.activeCategories.toString()
            )
            StatItem(
                label = "Root",
                value = stats.rootCategories.toString()
            )
            StatItem(
                label = "Max Depth",
                value = stats.maxDepth.toString()
            )
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun CategoryFilterPanel(
    filters: CategorySearchFilters,
    onFiltersChange: (CategorySearchFilters) -> Unit,
    onClearFilters: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Filters",
                    style = MaterialTheme.typography.titleMedium
                )
                TextButton(onClick = onClearFilters) {
                    Text("Clear All")
                }
            }
            
            // Search field
            OutlinedTextField(
                value = filters.searchQuery,
                onValueChange = { query ->
                    onFiltersChange(filters.copy(searchQuery = query))
                },
                label = { Text("Search categories") },
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = {
                    Icon(Icons.Default.Search, contentDescription = null)
                }
            )
            
            // Level filter
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text("Level:", style = MaterialTheme.typography.labelMedium)
                listOf("All", "0", "1", "2", "3+").forEachIndexed { index, level ->
                    FilterChip(
                        onClick = {
                            val newLevel = when (index) {
                                0 -> null
                                1, 2, 3 -> index - 1
                                else -> 3
                            }
                            onFiltersChange(filters.copy(level = newLevel))
                        },
                        label = { Text(level) },
                        selected = when (index) {
                            0 -> filters.level == null
                            1, 2, 3 -> filters.level == index - 1
                            else -> filters.level != null && filters.level!! >= 3
                        }
                    )
                }
            }
            
            // Status filter
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text("Status:", style = MaterialTheme.typography.labelMedium)
                listOf("All", "Active", "Inactive").forEachIndexed { index, status ->
                    FilterChip(
                        onClick = {
                            val newStatus = when (index) {
                                0 -> null
                                1 -> true
                                else -> false
                            }
                            onFiltersChange(filters.copy(isActive = newStatus))
                        },
                        label = { Text(status) },
                        selected = when (index) {
                            0 -> filters.isActive == null
                            1 -> filters.isActive == true
                            else -> filters.isActive == false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun SelectionActionsBar(
    selectedCount: Int,
    onActivate: () -> Unit,
    onDeactivate: () -> Unit,
    onDelete: () -> Unit,
    onClearSelection: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$selectedCount selected",
                style = MaterialTheme.typography.titleMedium
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                IconButton(onClick = onActivate) {
                    Icon(Icons.Default.CheckCircle, contentDescription = "Activate")
                }
                IconButton(onClick = onDeactivate) {
                    Icon(Icons.Default.Cancel, contentDescription = "Deactivate")
                }
                IconButton(onClick = onDelete) {
                    Icon(Icons.Default.Delete, contentDescription = "Delete")
                }
                IconButton(onClick = onClearSelection) {
                    Icon(Icons.Default.Clear, contentDescription = "Clear Selection")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CategoryListItem(
    category: Category,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Selection checkbox
            if (isSelectionMode) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onClick() }
                )
                Spacer(modifier = Modifier.width(12.dp))
            }

            // Category icon
            if (category.iconUrl.isNotEmpty()) {
                // TODO: Load category icon
                Icon(
                    Icons.Default.Category,
                    contentDescription = null,
                    modifier = Modifier.size(40.dp)
                )
            } else {
                Icon(
                    Icons.Default.Category,
                    contentDescription = null,
                    modifier = Modifier.size(40.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Category info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = category.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // Level indicator
                    if (category.level > 0) {
                        Surface(
                            color = MaterialTheme.colorScheme.secondaryContainer,
                            shape = MaterialTheme.shapes.small
                        ) {
                            Text(
                                text = "L${category.level}",
                                style = MaterialTheme.typography.labelSmall,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }

                    // Status indicator
                    if (!category.isActive) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Surface(
                            color = MaterialTheme.colorScheme.errorContainer,
                            shape = MaterialTheme.shapes.small
                        ) {
                            Text(
                                text = "Inactive",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onErrorContainer,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }

                if (category.description.isNotEmpty()) {
                    Text(
                        text = category.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 2
                    )
                }

                // Path and stats
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "/${category.path}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        if (category.productCount > 0) {
                            Text(
                                text = "${category.productCount} products",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        if (category.subcategoryCount > 0) {
                            Text(
                                text = "${category.subcategoryCount} subcategories",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.secondary
                            )
                        }
                    }
                }
            }

            // Action button
            if (!isSelectionMode) {
                IconButton(onClick = onClick) {
                    Icon(Icons.Default.ChevronRight, contentDescription = "View Details")
                }
            }
        }
    }
}
