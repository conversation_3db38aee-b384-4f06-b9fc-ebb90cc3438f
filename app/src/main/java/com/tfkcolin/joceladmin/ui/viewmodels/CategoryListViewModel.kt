package com.tfkcolin.joceladmin.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.CategorySearchFilters
import com.tfkcolin.joceladmin.data.models.CategoryStats
import com.tfkcolin.joceladmin.repository.CategoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for category list management
 */
@HiltViewModel
class CategoryListViewModel @Inject constructor(
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoryListUiState())
    val uiState: StateFlow<CategoryListUiState> = _uiState.asStateFlow()

    private val _searchFilters = MutableStateFlow(CategorySearchFilters())
    val searchFilters: StateFlow<CategorySearchFilters> = _searchFilters.asStateFlow()

    // Paginated categories flow
    val categories: Flow<PagingData<Category>> = categoryRepository
        .getCategories(_searchFilters.value)
        .cachedIn(viewModelScope)

    init {
        loadInitialData()
    }

    /**
     * Load initial data
     */
    private fun loadInitialData() {
        loadRootCategories()
        loadCategoryStats()
    }

    /**
     * Load root categories for hierarchy view
     */
    fun loadRootCategories() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingRootCategories = true) }
            
            categoryRepository.getRootCategories().fold(
                onSuccess = { categories ->
                    _uiState.update { 
                        it.copy(
                            rootCategories = categories,
                            isLoadingRootCategories = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingRootCategories = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Load subcategories for a parent category
     */
    fun loadSubcategories(parentId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingSubcategories = true) }
            
            categoryRepository.getSubcategories(parentId).fold(
                onSuccess = { subcategories ->
                    val currentSubcategories = _uiState.value.subcategoriesMap.toMutableMap()
                    currentSubcategories[parentId] = subcategories
                    
                    _uiState.update { 
                        it.copy(
                            subcategoriesMap = currentSubcategories,
                            isLoadingSubcategories = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingSubcategories = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Load category statistics
     */
    fun loadCategoryStats() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingStats = true) }
            
            categoryRepository.getCategoryStats().fold(
                onSuccess = { stats ->
                    _uiState.update { 
                        it.copy(
                            categoryStats = stats,
                            isLoadingStats = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingStats = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Update search filters
     */
    fun updateFilters(filters: CategorySearchFilters) {
        _searchFilters.value = filters
    }

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        val currentFilters = _searchFilters.value
        _searchFilters.value = currentFilters.copy(searchQuery = query)
    }

    /**
     * Filter by level
     */
    fun filterByLevel(level: Int?) {
        val currentFilters = _searchFilters.value
        _searchFilters.value = currentFilters.copy(level = level)
    }

    /**
     * Filter by active status
     */
    fun filterByActiveStatus(isActive: Boolean?) {
        val currentFilters = _searchFilters.value
        _searchFilters.value = currentFilters.copy(isActive = isActive)
    }

    /**
     * Toggle category expansion in hierarchy view
     */
    fun toggleCategoryExpansion(categoryId: String) {
        val currentExpanded = _uiState.value.expandedCategories.toMutableSet()
        if (currentExpanded.contains(categoryId)) {
            currentExpanded.remove(categoryId)
        } else {
            currentExpanded.add(categoryId)
            // Load subcategories if not already loaded
            if (!_uiState.value.subcategoriesMap.containsKey(categoryId)) {
                loadSubcategories(categoryId)
            }
        }
        
        _uiState.update { it.copy(expandedCategories = currentExpanded) }
    }

    /**
     * Select category for operations
     */
    fun selectCategory(categoryId: String) {
        val currentSelected = _uiState.value.selectedCategories.toMutableSet()
        if (currentSelected.contains(categoryId)) {
            currentSelected.remove(categoryId)
        } else {
            currentSelected.add(categoryId)
        }
        
        _uiState.update { it.copy(selectedCategories = currentSelected) }
    }

    /**
     * Clear all selections
     */
    fun clearSelections() {
        _uiState.update { it.copy(selectedCategories = emptySet()) }
    }

    /**
     * Toggle selection mode
     */
    fun toggleSelectionMode() {
        val newSelectionMode = !_uiState.value.isSelectionMode
        _uiState.update { 
            it.copy(
                isSelectionMode = newSelectionMode,
                selectedCategories = if (!newSelectionMode) emptySet() else it.selectedCategories
            )
        }
    }

    /**
     * Delete selected categories
     */
    fun deleteSelectedCategories() {
        val selectedIds = _uiState.value.selectedCategories
        if (selectedIds.isEmpty()) return

        viewModelScope.launch {
            _uiState.update { it.copy(isDeleting = true) }
            
            var successCount = 0
            var errorCount = 0
            
            selectedIds.forEach { categoryId ->
                categoryRepository.deleteCategory(categoryId).fold(
                    onSuccess = { successCount++ },
                    onFailure = { errorCount++ }
                )
            }
            
            _uiState.update { 
                it.copy(
                    isDeleting = false,
                    selectedCategories = emptySet(),
                    isSelectionMode = false,
                    message = if (errorCount == 0) {
                        "Successfully deleted $successCount categories"
                    } else {
                        "Deleted $successCount categories, $errorCount failed"
                    }
                )
            }
            
            // Refresh data
            loadRootCategories()
            loadCategoryStats()
        }
    }

    /**
     * Activate/deactivate selected categories
     */
    fun toggleSelectedCategoriesStatus(activate: Boolean) {
        val selectedIds = _uiState.value.selectedCategories
        if (selectedIds.isEmpty()) return

        viewModelScope.launch {
            _uiState.update { it.copy(isUpdating = true) }
            
            var successCount = 0
            var errorCount = 0
            
            selectedIds.forEach { categoryId ->
                categoryRepository.updateCategoryStatus(categoryId, activate).fold(
                    onSuccess = { successCount++ },
                    onFailure = { errorCount++ }
                )
            }
            
            _uiState.update { 
                it.copy(
                    isUpdating = false,
                    selectedCategories = emptySet(),
                    isSelectionMode = false,
                    message = if (errorCount == 0) {
                        val action = if (activate) "activated" else "deactivated"
                        "Successfully $action $successCount categories"
                    } else {
                        val action = if (activate) "activated" else "deactivated"
                        "$action $successCount categories, $errorCount failed"
                    }
                )
            }
            
            // Refresh data
            loadRootCategories()
            loadCategoryStats()
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * Clear info message
     */
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }

    /**
     * Refresh all data
     */
    fun refresh() {
        loadInitialData()
    }
}

/**
 * UI state for category list screen
 */
data class CategoryListUiState(
    val rootCategories: List<Category> = emptyList(),
    val subcategoriesMap: Map<String, List<Category>> = emptyMap(),
    val categoryStats: CategoryStats? = null,
    val expandedCategories: Set<String> = emptySet(),
    val selectedCategories: Set<String> = emptySet(),
    val isSelectionMode: Boolean = false,
    val isLoadingRootCategories: Boolean = false,
    val isLoadingSubcategories: Boolean = false,
    val isLoadingStats: Boolean = false,
    val isDeleting: Boolean = false,
    val isUpdating: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
