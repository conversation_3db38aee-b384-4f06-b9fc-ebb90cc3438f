package com.tfkcolin.joceladmin.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.CategoryCreateRequest
import com.tfkcolin.joceladmin.data.models.CategoryUpdateRequest
import com.tfkcolin.joceladmin.data.models.CategorySEO
import com.tfkcolin.joceladmin.data.models.CategoryMetadata
import com.tfkcolin.joceladmin.repository.CategoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for category details (create/edit)
 */
@HiltViewModel
class CategoryDetailsViewModel @Inject constructor(
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoryDetailsUiState())
    val uiState: StateFlow<CategoryDetailsUiState> = _uiState.asStateFlow()

    /**
     * Initialize for editing existing category
     */
    fun initializeForEdit(categoryId: String) {
        if (categoryId.isEmpty()) {
            // New category mode
            _uiState.update { it.copy(isEditMode = false) }
            loadParentCategories()
            return
        }

        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, isEditMode = true) }
            
            categoryRepository.getCategoryById(categoryId).fold(
                onSuccess = { category ->
                    if (category != null) {
                        _uiState.update { 
                            it.copy(
                                formData = CategoryFormData.fromCategory(category),
                                originalCategory = category,
                                isLoading = false
                            )
                        }
                        loadParentCategories()
                    } else {
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                error = "Category not found"
                            )
                        }
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Load available parent categories
     */
    private fun loadParentCategories() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingParents = true) }
            
            categoryRepository.getAllCategories().fold(
                onSuccess = { categories ->
                    // Filter out current category and its descendants to prevent circular references
                    val currentCategoryId = _uiState.value.originalCategory?.id
                    val availableParents = if (currentCategoryId != null) {
                        categories.filter { category ->
                            category.id != currentCategoryId && 
                            !category.path.contains(currentCategoryId)
                        }
                    } else {
                        categories
                    }
                    
                    _uiState.update { 
                        it.copy(
                            availableParents = availableParents,
                            isLoadingParents = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingParents = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Update form data
     */
    fun updateFormData(formData: CategoryFormData) {
        _uiState.update { it.copy(formData = formData) }
    }

    /**
     * Update category name
     */
    fun updateName(name: String) {
        val currentFormData = _uiState.value.formData
        _uiState.update { 
            it.copy(
                formData = currentFormData.copy(
                    name = name,
                    slug = if (currentFormData.slug.isEmpty()) generateSlug(name) else currentFormData.slug
                )
            )
        }
    }

    /**
     * Update category description
     */
    fun updateDescription(description: String) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(description = description)) }
    }

    /**
     * Update category slug
     */
    fun updateSlug(slug: String) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(slug = slug)) }
    }

    /**
     * Update parent category
     */
    fun updateParentCategory(parentId: String?) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(parentId = parentId)) }
    }

    /**
     * Update sort order
     */
    fun updateSortOrder(sortOrder: Int) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(sortOrder = sortOrder)) }
    }

    /**
     * Update icon URL
     */
    fun updateIconUrl(iconUrl: String) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(iconUrl = iconUrl)) }
    }

    /**
     * Update banner URL
     */
    fun updateBannerUrl(bannerUrl: String) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(bannerUrl = bannerUrl)) }
    }

    /**
     * Update visibility status
     */
    fun updateVisibility(isVisible: Boolean) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(isVisible = isVisible)) }
    }

    /**
     * Update SEO data
     */
    fun updateSeoData(seoData: CategorySEO) {
        val currentFormData = _uiState.value.formData
        _uiState.update { it.copy(formData = currentFormData.copy(seoData = seoData)) }
    }

    /**
     * Validate form data
     */
    private fun validateForm(formData: CategoryFormData): Boolean {
        val errors = mutableListOf<String>()
        
        if (formData.name.isBlank()) {
            errors.add("Category name is required")
        }
        
        if (formData.slug.isBlank()) {
            errors.add("Category slug is required")
        }
        
        if (formData.sortOrder < 0) {
            errors.add("Sort order must be non-negative")
        }
        
        _uiState.update { it.copy(validationErrors = errors) }
        return errors.isEmpty()
    }

    /**
     * Save category (create or update)
     */
    fun saveCategory() {
        val formData = _uiState.value.formData
        
        if (!validateForm(formData)) {
            return
        }

        viewModelScope.launch {
            _uiState.update { it.copy(isSaving = true) }
            
            val result = if (_uiState.value.isEditMode) {
                updateCategory(formData)
            } else {
                createCategory(formData)
            }
            
            result.fold(
                onSuccess = { category ->
                    _uiState.update { 
                        it.copy(
                            isSaving = false,
                            savedCategory = category,
                            message = if (_uiState.value.isEditMode) {
                                "Category updated successfully"
                            } else {
                                "Category created successfully"
                            }
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isSaving = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Create new category
     */
    private suspend fun createCategory(formData: CategoryFormData): Result<Category> {
        val request = CategoryCreateRequest(
            name = formData.name,
            description = formData.description,
            slug = formData.slug,
            parentId = formData.parentId,
            iconUrl = formData.iconUrl,
            bannerUrl = formData.bannerUrl,
            sortOrder = formData.sortOrder,
            seoData = formData.seoData,
            metadata = formData.metadata
        )
        
        return categoryRepository.createCategory(request)
    }

    /**
     * Update existing category
     */
    private suspend fun updateCategory(formData: CategoryFormData): Result<Category> {
        val categoryId = _uiState.value.originalCategory?.id 
            ?: return Result.failure(IllegalStateException("No category to update"))
        
        val request = CategoryUpdateRequest(
            id = categoryId,
            name = formData.name,
            description = formData.description,
            slug = formData.slug,
            parentId = formData.parentId,
            iconUrl = formData.iconUrl,
            bannerUrl = formData.bannerUrl,
            sortOrder = formData.sortOrder,
            isVisible = formData.isVisible,
            seoData = formData.seoData,
            metadata = formData.metadata
        )
        
        return categoryRepository.updateCategory(categoryId, request)
    }

    /**
     * Generate slug from name
     */
    private fun generateSlug(name: String): String {
        return name.lowercase()
            .replace(Regex("[^a-z0-9\\s-]"), "")
            .replace(Regex("\\s+"), "-")
            .trim('-')
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * Clear info message
     */
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }

    /**
     * Clear validation errors
     */
    fun clearValidationErrors() {
        _uiState.update { it.copy(validationErrors = emptyList()) }
    }
}

/**
 * UI state for category details screen
 */
data class CategoryDetailsUiState(
    val formData: CategoryFormData = CategoryFormData(),
    val originalCategory: Category? = null,
    val availableParents: List<Category> = emptyList(),
    val savedCategory: Category? = null,
    val isEditMode: Boolean = false,
    val isLoading: Boolean = false,
    val isLoadingParents: Boolean = false,
    val isSaving: Boolean = false,
    val validationErrors: List<String> = emptyList(),
    val error: String? = null,
    val message: String? = null
)

/**
 * Form data for category creation/editing
 */
data class CategoryFormData(
    val name: String = "",
    val description: String = "",
    val slug: String = "",
    val parentId: String? = null,
    val iconUrl: String = "",
    val bannerUrl: String = "",
    val sortOrder: Int = 0,
    val isVisible: Boolean = true,
    val seoData: CategorySEO = CategorySEO(),
    val metadata: CategoryMetadata = CategoryMetadata()
) {
    companion object {
        fun fromCategory(category: Category): CategoryFormData {
            return CategoryFormData(
                name = category.name,
                description = category.description,
                slug = category.slug,
                parentId = category.parentId,
                iconUrl = category.iconUrl,
                bannerUrl = category.bannerUrl,
                sortOrder = category.sortOrder,
                isVisible = category.isVisible,
                seoData = category.seoData,
                metadata = category.metadata
            )
        }
    }
}
