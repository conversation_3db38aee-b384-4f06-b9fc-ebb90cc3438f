package com.tfkcolin.joceladmin.ui.screens.product

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.ProductSearchFilters

/**
 * Search bar component with advanced filtering
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductSearchBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onSearchSubmit: () -> Unit,
    onFilterClick: () -> Unit,
    hasActiveFilters: Boolean = false,
    modifier: Modifier = Modifier
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    OutlinedTextField(
        value = searchQuery,
        onValueChange = onSearchQueryChange,
        modifier = modifier.fillMaxWidth(),
        placeholder = { Text("Search products...") },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "Search"
            )
        },
        trailingIcon = {
            Row {
                if (searchQuery.isNotEmpty()) {
                    IconButton(
                        onClick = { onSearchQueryChange("") }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "Clear search"
                        )
                    }
                }
                
                IconButton(
                    onClick = onFilterClick
                ) {
                    Icon(
                        imageVector = if (hasActiveFilters) Icons.Default.FilterAlt else Icons.Default.FilterList,
                        contentDescription = "Filters",
                        tint = if (hasActiveFilters) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        },
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Text,
            imeAction = ImeAction.Search
        ),
        keyboardActions = KeyboardActions(
            onSearch = {
                onSearchSubmit()
                keyboardController?.hide()
            }
        ),
        singleLine = true
    )
}

/**
 * Quick filter chips component
 */
@Composable
fun QuickFilterChips(
    categories: List<Category>,
    selectedCategory: String?,
    onCategorySelect: (String?) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        // All categories chip
        item {
            FilterChip(
                onClick = { onCategorySelect(null) },
                label = { Text("All") },
                selected = selectedCategory == null,
                leadingIcon = if (selectedCategory == null) {
                    {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            modifier = Modifier.size(FilterChipDefaults.IconSize)
                        )
                    }
                } else null
            )
        }
        
        // Category chips
        items(categories) { category ->
            FilterChip(
                onClick = { 
                    onCategorySelect(
                        if (selectedCategory == category.name) null else category.name
                    )
                },
                label = { Text(category.name) },
                selected = selectedCategory == category.name,
                leadingIcon = if (selectedCategory == category.name) {
                    {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            modifier = Modifier.size(FilterChipDefaults.IconSize)
                        )
                    }
                } else null
            )
        }
    }
}

/**
 * Advanced filter panel component
 */
@Composable
fun AdvancedFilterPanel(
    filters: ProductSearchFilters,
    categories: List<Category>,
    onFiltersChange: (ProductSearchFilters) -> Unit,
    onClearFilters: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Advanced Filters",
                    style = MaterialTheme.typography.titleMedium
                )
                
                TextButton(onClick = onClearFilters) {
                    Text("Clear All")
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Category filter
            CategoryDropdown(
                selectedCategory = filters.categories.firstOrNull(),
                categories = categories,
                onCategorySelect = { category ->
                    onFiltersChange(filters.copy(categories = if (category != null) listOf(category) else emptyList()))
                }
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Brand filter
            OutlinedTextField(
                value = filters.brands.firstOrNull() ?: "",
                onValueChange = { brand ->
                    onFiltersChange(filters.copy(brands = if (brand.isNotEmpty()) listOf(brand) else emptyList()))
                },
                label = { Text("Brand") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Availability filters
            Text(
                text = "Availability",
                style = MaterialTheme.typography.labelLarge
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                FilterChip(
                    onClick = { 
                        onFiltersChange(
                            filters.copy(
                                isAvailable = if (filters.isAvailable == true) null else true
                            )
                        )
                    },
                    label = { Text("Available") },
                    selected = filters.isAvailable == true
                )
                
                FilterChip(
                    onClick = { 
                        onFiltersChange(
                            filters.copy(
                                isFeatured = if (filters.isFeatured == true) null else true
                            )
                        )
                    },
                    label = { Text("Featured") },
                    selected = filters.isFeatured == true
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Price range (simplified for now)
            Text(
                text = "Price Range",
                style = MaterialTheme.typography.labelLarge
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = filters.priceRange?.minPrice?.toString() ?: "",
                    onValueChange = { value ->
                        val minPrice = value.toDoubleOrNull()
                        val currentRange = filters.priceRange
                        onFiltersChange(
                            filters.copy(
                                priceRange = if (minPrice != null || currentRange?.maxPrice != null) {
                                    com.tfkcolin.joceladmin.data.models.PriceRange(
                                        minPrice = minPrice ?: 0.0,
                                        maxPrice = currentRange?.maxPrice ?: Double.MAX_VALUE
                                    )
                                } else null
                            )
                        )
                    },
                    label = { Text("Min Price") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = filters.priceRange?.maxPrice?.let { 
                        if (it == Double.MAX_VALUE) "" else it.toString() 
                    } ?: "",
                    onValueChange = { value ->
                        val maxPrice = value.toDoubleOrNull()
                        val currentRange = filters.priceRange
                        onFiltersChange(
                            filters.copy(
                                priceRange = if (maxPrice != null || currentRange?.minPrice != null) {
                                    com.tfkcolin.joceladmin.data.models.PriceRange(
                                        minPrice = currentRange?.minPrice ?: 0.0,
                                        maxPrice = maxPrice ?: Double.MAX_VALUE
                                    )
                                } else null
                            )
                        )
                    },
                    label = { Text("Max Price") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true
                )
            }
        }
    }
}

/**
 * Category dropdown component
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryDropdown(
    selectedCategory: String?,
    categories: List<Category>,
    onCategorySelect: (String?) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded },
        modifier = modifier
    ) {
        OutlinedTextField(
            value = selectedCategory ?: "All Categories",
            onValueChange = { },
            readOnly = true,
            label = { Text("Category") },
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )
        
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DropdownMenuItem(
                text = { Text("All Categories") },
                onClick = {
                    onCategorySelect(null)
                    expanded = false
                }
            )
            
            categories.forEach { category ->
                DropdownMenuItem(
                    text = { Text(category.name) },
                    onClick = {
                        onCategorySelect(category.name)
                        expanded = false
                    }
                )
            }
        }
    }
}

/**
 * Active filters display component
 */
@Composable
fun ActiveFiltersDisplay(
    filters: ProductSearchFilters,
    onRemoveFilter: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val activeFilters = mutableListOf<Pair<String, String>>()

    filters.categories.firstOrNull()?.let { activeFilters.add("category" to "Category: $it") }
    filters.genres.firstOrNull()?.let { activeFilters.add("genre" to "Genre: $it") }
    filters.brands.firstOrNull()?.let { activeFilters.add("brand" to "Brand: $it") }
    filters.isAvailable?.let { if (it) activeFilters.add("available" to "Available") }
    filters.isFeatured?.let { if (it) activeFilters.add("featured" to "Featured") }
    filters.priceRange?.let {
        activeFilters.add("price" to "Price: ${it.minPrice}-${if (it.maxPrice == Double.MAX_VALUE) "∞" else it.maxPrice}")
    }
    
    if (activeFilters.isNotEmpty()) {
        LazyRow(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(activeFilters) { (key, label) ->
                InputChip(
                    onClick = { onRemoveFilter(key) },
                    label = { Text(label) },
                    selected = true,
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Remove filter",
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
            }
        }
    }
}
