package com.tfkcolin.joceladmin.ui.screens.category

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.CategorySEO
import com.tfkcolin.joceladmin.ui.components.common.JACTopAppBar
import com.tfkcolin.joceladmin.ui.viewmodels.CategoryDetailsViewModel
import com.tfkcolin.joceladmin.ui.viewmodels.CategoryFormData

/**
 * Screen for creating/editing category details
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryDetailsScreen(
    categoryId: String,
    onNavigateBack: () -> Unit,
    onNavigateToGenres: (String) -> Unit = {},
    viewModel: CategoryDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val scrollState = rememberScrollState()
    
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showSeoDialog by remember { mutableStateOf(false) }

    // Initialize viewmodel
    LaunchedEffect(categoryId) {
        viewModel.initializeForEdit(categoryId)
    }

    // Handle navigation after save
    LaunchedEffect(uiState.savedCategory) {
        uiState.savedCategory?.let {
            onNavigateBack()
        }
    }

    // Handle messages
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // Show error snackbar
            viewModel.clearError()
        }
    }

    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // Show success snackbar
            viewModel.clearMessage()
        }
    }

    Scaffold(
        topBar = {
            JACTopAppBar(
                title = if (uiState.isEditMode) "Edit Category" else "New Category",
                onNavigationClick = onNavigateBack,
                actions = {
                    // Save button
                    TextButton(
                        onClick = viewModel::saveCategory,
                        enabled = !uiState.isSaving
                    ) {
                        if (uiState.isSaving) {
                            CircularProgressIndicator(modifier = Modifier.size(16.dp))
                        } else {
                            Text("Save")
                        }
                    }
                    
                    // More options
                    if (uiState.isEditMode) {
                        var showMenu by remember { mutableStateOf(false) }
                        Box {
                            IconButton(onClick = { showMenu = true }) {
                                Icon(Icons.Default.MoreVert, contentDescription = "More Options")
                            }
                            DropdownMenu(
                                expanded = showMenu,
                                onDismissRequest = { showMenu = false }
                            ) {
                                DropdownMenuItem(
                                    text = { Text("Manage Genres") },
                                    onClick = {
                                        showMenu = false
                                        val category = uiState.originalCategory
                                        if (category != null) {
                                            onNavigateToGenres(category.id)
                                        }
                                    },
                                    leadingIcon = { Icon(Icons.Default.Category, contentDescription = null) }
                                )
                                DropdownMenuItem(
                                    text = { Text("SEO Settings") },
                                    onClick = {
                                        showMenu = false
                                        showSeoDialog = true
                                    },
                                    leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) }
                                )
                                Divider()
                                DropdownMenuItem(
                                    text = { Text("Delete Category") },
                                    onClick = {
                                        showMenu = false
                                        showDeleteDialog = true
                                    },
                                    leadingIcon = { Icon(Icons.Default.Delete, contentDescription = null) }
                                )
                            }
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        if (uiState.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .verticalScroll(scrollState)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Validation errors
                if (uiState.validationErrors.isNotEmpty()) {
                    ValidationErrorsCard(
                        errors = uiState.validationErrors,
                        onDismiss = viewModel::clearValidationErrors
                    )
                }

                // Basic Information
                BasicInformationSection(
                    formData = uiState.formData,
                    onFormDataChange = viewModel::updateFormData,
                    onNameChange = viewModel::updateName,
                    onDescriptionChange = viewModel::updateDescription,
                    onSlugChange = viewModel::updateSlug
                )

                // Hierarchy Section
                HierarchySection(
                    formData = uiState.formData,
                    availableParents = uiState.availableParents,
                    isLoadingParents = uiState.isLoadingParents,
                    onParentChange = viewModel::updateParentCategory,
                    onSortOrderChange = viewModel::updateSortOrder
                )

                // Visual Settings
                VisualSettingsSection(
                    formData = uiState.formData,
                    onIconUrlChange = viewModel::updateIconUrl,
                    onBannerUrlChange = viewModel::updateBannerUrl,
                    onVisibilityChange = viewModel::updateVisibility
                )

                // Category Statistics (Edit mode only)
                val originalCategory = uiState.originalCategory
                if (uiState.isEditMode && originalCategory != null) {
                    CategoryStatisticsSection(
                        category = originalCategory
                    )
                }
            }
        }
    }

    // Delete Confirmation Dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("Delete Category") },
            text = { 
                Text("Are you sure you want to delete this category? This action cannot be undone and will affect all subcategories and products.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        // TODO: Implement delete functionality
                    }
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    // SEO Settings Dialog
    if (showSeoDialog) {
        SeoSettingsDialog(
            seoData = uiState.formData.seoData,
            onSeoDataChange = viewModel::updateSeoData,
            onDismiss = { showSeoDialog = false }
        )
    }
}

@Composable
private fun ValidationErrorsCard(
    errors: List<String>,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Validation Errors",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    fontWeight = FontWeight.Bold
                )
                IconButton(onClick = onDismiss) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "Dismiss",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
            
            errors.forEach { error ->
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = error,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

@Composable
private fun BasicInformationSection(
    formData: CategoryFormData,
    onFormDataChange: (CategoryFormData) -> Unit,
    onNameChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onSlugChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Basic Information",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            // Category Name
            OutlinedTextField(
                value = formData.name,
                onValueChange = onNameChange,
                label = { Text("Category Name *") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // Description
            OutlinedTextField(
                value = formData.description,
                onValueChange = onDescriptionChange,
                label = { Text("Description") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 5
            )
            
            // Slug
            OutlinedTextField(
                value = formData.slug,
                onValueChange = onSlugChange,
                label = { Text("URL Slug *") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                supportingText = {
                    Text("Used in URLs. Only lowercase letters, numbers, and hyphens allowed.")
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HierarchySection(
    formData: CategoryFormData,
    availableParents: List<Category>,
    isLoadingParents: Boolean,
    onParentChange: (String?) -> Unit,
    onSortOrderChange: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Hierarchy & Organization",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // Parent Category
            var expandedParent by remember { mutableStateOf(false) }
            ExposedDropdownMenuBox(
                expanded = expandedParent,
                onExpandedChange = { expandedParent = it }
            ) {
                OutlinedTextField(
                    value = availableParents.find { it.id == formData.parentId }?.name ?: "None (Root Category)",
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("Parent Category") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor(),
                    trailingIcon = {
                        if (isLoadingParents) {
                            CircularProgressIndicator(modifier = Modifier.size(20.dp))
                        } else {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedParent)
                        }
                    }
                )

                ExposedDropdownMenu(
                    expanded = expandedParent,
                    onDismissRequest = { expandedParent = false }
                ) {
                    DropdownMenuItem(
                        text = { Text("None (Root Category)") },
                        onClick = {
                            onParentChange(null)
                            expandedParent = false
                        }
                    )

                    availableParents.forEach { parent ->
                        DropdownMenuItem(
                            text = {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text("${"  ".repeat(parent.level)}${parent.name}")
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "L${parent.level}",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            },
                            onClick = {
                                onParentChange(parent.id)
                                expandedParent = false
                            }
                        )
                    }
                }
            }

            // Sort Order
            OutlinedTextField(
                value = formData.sortOrder.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { onSortOrderChange(it) }
                },
                label = { Text("Sort Order") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                supportingText = {
                    Text("Lower numbers appear first. Default is 0.")
                }
            )
        }
    }
}

@Composable
private fun VisualSettingsSection(
    formData: CategoryFormData,
    onIconUrlChange: (String) -> Unit,
    onBannerUrlChange: (String) -> Unit,
    onVisibilityChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Visual Settings",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // Icon URL
            OutlinedTextField(
                value = formData.iconUrl,
                onValueChange = onIconUrlChange,
                label = { Text("Icon URL") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                supportingText = {
                    Text("URL to category icon image")
                }
            )

            // Banner URL
            OutlinedTextField(
                value = formData.bannerUrl,
                onValueChange = onBannerUrlChange,
                label = { Text("Banner URL") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                supportingText = {
                    Text("URL to category banner image")
                }
            )

            // Visibility Toggle
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Visible to Users",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Controls whether this category appears in public listings",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Switch(
                    checked = formData.isVisible,
                    onCheckedChange = onVisibilityChange
                )
            }
        }
    }
}

@Composable
private fun CategoryStatisticsSection(
    category: Category,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Statistics",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "Products",
                    value = category.productCount.toString()
                )
                StatisticItem(
                    label = "Subcategories",
                    value = category.subcategoryCount.toString()
                )
                StatisticItem(
                    label = "Level",
                    value = category.level.toString()
                )
                StatisticItem(
                    label = "Status",
                    value = if (category.isActive) "Active" else "Inactive"
                )
            }

            // Category Path
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.AccountTree,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Path: /${category.path}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun StatisticItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun SeoSettingsDialog(
    seoData: CategorySEO,
    onSeoDataChange: (CategorySEO) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("SEO Settings") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedTextField(
                    value = seoData.metaTitle,
                    onValueChange = { title ->
                        onSeoDataChange(seoData.copy(metaTitle = title))
                    },
                    label = { Text("Meta Title") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                OutlinedTextField(
                    value = seoData.metaDescription,
                    onValueChange = { description ->
                        onSeoDataChange(seoData.copy(metaDescription = description))
                    },
                    label = { Text("Meta Description") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 4
                )

                OutlinedTextField(
                    value = seoData.keywords.joinToString(", "),
                    onValueChange = { keywords ->
                        val keywordList = keywords.split(",").map { it.trim() }.filter { it.isNotEmpty() }
                        onSeoDataChange(seoData.copy(keywords = keywordList))
                    },
                    label = { Text("Keywords") },
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        Text("Separate keywords with commas")
                    }
                )
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Done")
            }
        }
    )
}
