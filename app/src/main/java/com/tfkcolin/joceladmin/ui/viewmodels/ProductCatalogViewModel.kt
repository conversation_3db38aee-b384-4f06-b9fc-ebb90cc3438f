package com.tfkcolin.joceladmin.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.joceladmin.data.models.Product
import com.tfkcolin.joceladmin.data.models.ProductSearchFilters
import com.tfkcolin.joceladmin.data.models.ProductStats
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.repository.ProductRepository
import com.tfkcolin.joceladmin.repository.CategoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for product catalog management
 */
@HiltViewModel
class ProductCatalogViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProductCatalogUiState())
    val uiState: StateFlow<ProductCatalogUiState> = _uiState.asStateFlow()

    private val _searchFilters = MutableStateFlow(ProductSearchFilters())
    val searchFilters: StateFlow<ProductSearchFilters> = _searchFilters.asStateFlow()

    // Paginated products flow that reacts to filter changes
    val products: Flow<PagingData<Product>> = _searchFilters
        .flatMapLatest { filters ->
            productRepository.getProducts(filters)
        }
        .cachedIn(viewModelScope)

    init {
        loadInitialData()
    }

    /**
     * Load initial data
     */
    private fun loadInitialData() {
        loadCategories()
        loadProductStats()
    }

    /**
     * Load categories for filtering
     */
    private fun loadCategories() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingCategories = true) }
            
            categoryRepository.getRootCategories()
                .onSuccess { categories ->
                    _uiState.update { 
                        it.copy(
                            categories = categories,
                            isLoadingCategories = false
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            error = error.message,
                            isLoadingCategories = false
                        )
                    }
                }
        }
    }

    /**
     * Load product statistics
     */
    private fun loadProductStats() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingStats = true) }
            
            productRepository.getProductStats()
                .onSuccess { stats ->
                    _uiState.update { 
                        it.copy(
                            productStats = stats,
                            isLoadingStats = false
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            error = error.message,
                            isLoadingStats = false
                        )
                    }
                }
        }
    }

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchFilters.update { it.copy(searchQuery = query) }
    }

    /**
     * Update category filter
     */
    fun updateCategoryFilter(category: String?) {
        _searchFilters.update {
            it.copy(categories = if (category != null) listOf(category) else emptyList())
        }
    }

    /**
     * Update genre filter
     */
    fun updateGenreFilter(genre: String?) {
        _searchFilters.update {
            it.copy(genres = if (genre != null) listOf(genre) else emptyList())
        }
    }

    /**
     * Update brand filter
     */
    fun updateBrandFilter(brand: String?) {
        _searchFilters.update {
            it.copy(brands = if (brand != null) listOf(brand) else emptyList())
        }
    }

    /**
     * Update availability filter
     */
    fun updateAvailabilityFilter(isAvailable: Boolean?) {
        _searchFilters.update { it.copy(isAvailable = isAvailable) }
    }

    /**
     * Update featured filter
     */
    fun updateFeaturedFilter(isFeatured: Boolean?) {
        _searchFilters.update { it.copy(isFeatured = isFeatured) }
    }

    /**
     * Clear all filters
     */
    fun clearFilters() {
        _searchFilters.value = ProductSearchFilters()
    }

    /**
     * Toggle view mode between grid and list
     */
    fun toggleViewMode() {
        _uiState.update { 
            it.copy(
                viewMode = if (it.viewMode == ViewMode.GRID) ViewMode.LIST else ViewMode.GRID
            )
        }
    }

    /**
     * Toggle selection mode
     */
    fun toggleSelectionMode() {
        _uiState.update { 
            it.copy(
                isSelectionMode = !it.isSelectionMode,
                selectedProducts = if (it.isSelectionMode) emptySet() else it.selectedProducts
            )
        }
    }

    /**
     * Toggle product selection
     */
    fun toggleProductSelection(productId: String) {
        _uiState.update { state ->
            val selectedProducts = state.selectedProducts.toMutableSet()
            if (selectedProducts.contains(productId)) {
                selectedProducts.remove(productId)
            } else {
                selectedProducts.add(productId)
            }
            state.copy(selectedProducts = selectedProducts)
        }
    }

    /**
     * Select all products
     */
    fun selectAllProducts() {
        // This would need to be implemented with the current page of products
        // For now, we'll just clear selection
        _uiState.update { it.copy(selectedProducts = emptySet()) }
    }

    /**
     * Clear selection
     */
    fun clearSelection() {
        _uiState.update { it.copy(selectedProducts = emptySet()) }
    }

    /**
     * Delete selected products
     */
    fun deleteSelectedProducts() {
        viewModelScope.launch {
            val selectedIds = _uiState.value.selectedProducts
            if (selectedIds.isEmpty()) return@launch

            _uiState.update { it.copy(isLoading = true) }

            try {
                selectedIds.forEach { productId ->
                    productRepository.deleteProduct(productId)
                }
                
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        selectedProducts = emptySet(),
                        isSelectionMode = false
                    )
                }
                
                // Refresh stats after deletion
                loadProductStats()
                
            } catch (error: Exception) {
                _uiState.update { 
                    it.copy(
                        error = error.message,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * Refresh data
     */
    fun refresh() {
        loadInitialData()
    }
}

/**
 * UI state for product catalog screen
 */
data class ProductCatalogUiState(
    val categories: List<Category> = emptyList(),
    val productStats: ProductStats? = null,
    val viewMode: ViewMode = ViewMode.GRID,
    val isSelectionMode: Boolean = false,
    val selectedProducts: Set<String> = emptySet(),
    val isLoading: Boolean = false,
    val isLoadingCategories: Boolean = false,
    val isLoadingStats: Boolean = false,
    val error: String? = null
)

/**
 * View mode for product display
 */
enum class ViewMode {
    GRID, LIST
}
