package com.tfkcolin.joceladmin.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.joceladmin.data.models.*
import com.tfkcolin.joceladmin.repository.CategoryRepository
import com.tfkcolin.joceladmin.repository.ImageDataRepository
import com.tfkcolin.joceladmin.services.ImageUploadService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for managing image list and operations
 */
@OptIn(ExperimentalCoroutinesApi::class)
@HiltViewModel
class ImageListViewModel @Inject constructor(
    private val imageDataRepository: ImageDataRepository,
    private val categoryRepository: CategoryRepository,
    private val imageUploadService: ImageUploadService
) : ViewModel() {

    private val _uiState = MutableStateFlow(ImageListUiState())
    val uiState: StateFlow<ImageListUiState> = _uiState.asStateFlow()

    private val _searchFilters = MutableStateFlow(ImageSearchFilters())
    val searchFilters: StateFlow<ImageSearchFilters> = _searchFilters.asStateFlow()

    // Paged images flow that reacts to filter changes
    val images: Flow<PagingData<ImageData>> = _searchFilters
        .flatMapLatest { filters ->
            imageDataRepository.getImages(filters)
        }
        .cachedIn(viewModelScope)

    init {
        loadCategories()
        loadImageStats()
    }

    /**
     * Load available categories for filtering
     */
    private fun loadCategories() {
        viewModelScope.launch {
            categoryRepository.getAllCategories().fold(
                onSuccess = { categories ->
                    _uiState.update {
                        it.copy(categories = categories)
                    }
                },
                onFailure = { error ->
                    _uiState.update {
                        it.copy(error = error.message)
                    }
                }
            )
        }
    }

    /**
     * Load image statistics
     */
    private fun loadImageStats() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingStats = true) }
            
            imageDataRepository.getImageStats().fold(
                onSuccess = { stats ->
                    _uiState.update { 
                        it.copy(
                            imageStats = stats,
                            isLoadingStats = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingStats = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Update search filters
     */
    fun updateFilters(filters: ImageSearchFilters) {
        _searchFilters.value = filters
    }

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        val currentFilters = _searchFilters.value
        _searchFilters.value = currentFilters.copy(searchQuery = query)
    }

    /**
     * Filter by category
     */
    fun filterByCategory(category: String) {
        val currentFilters = _searchFilters.value
        val updatedCategories = if (category.isEmpty()) {
            emptyList()
        } else {
            listOf(category)
        }
        _searchFilters.value = currentFilters.copy(categories = updatedCategories)
    }

    /**
     * Filter by genre
     */
    fun filterByGenre(genre: String) {
        val currentFilters = _searchFilters.value
        val updatedGenres = if (genre.isEmpty()) {
            emptyList()
        } else {
            listOf(genre)
        }
        _searchFilters.value = currentFilters.copy(genres = updatedGenres)
    }

    /**
     * Filter by format
     */
    fun filterByFormat(format: String?) {
        val currentFilters = _searchFilters.value
        _searchFilters.value = currentFilters.copy(format = format)
    }

    /**
     * Toggle active filter
     */
    fun toggleActiveFilter() {
        val currentFilters = _searchFilters.value
        val newActiveFilter = when (currentFilters.isActive) {
            null -> true
            true -> false
            false -> null
        }
        _searchFilters.value = currentFilters.copy(isActive = newActiveFilter)
    }

    /**
     * Clear all filters
     */
    fun clearFilters() {
        _searchFilters.value = ImageSearchFilters()
    }

    /**
     * Delete image
     */
    fun deleteImage(imageData: ImageData) {
        viewModelScope.launch {
            _uiState.update { it.copy(isDeleting = true) }
            
            imageUploadService.deleteImage(imageData).fold(
                onSuccess = {
                    _uiState.update { 
                        it.copy(
                            isDeleting = false,
                            deletedImageId = imageData.id
                        )
                    }
                    // Refresh stats after deletion
                    loadImageStats()
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isDeleting = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Delete multiple images
     */
    fun deleteMultipleImages(imageIds: List<String>) {
        viewModelScope.launch {
            _uiState.update { it.copy(isDeleting = true) }
            
            imageDataRepository.deleteImages(imageIds).fold(
                onSuccess = { deletedCount ->
                    _uiState.update { 
                        it.copy(
                            isDeleting = false,
                            deletedCount = deletedCount
                        )
                    }
                    // Refresh stats after deletion
                    loadImageStats()
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isDeleting = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Increment download count
     */
    fun incrementDownloadCount(imageId: String) {
        viewModelScope.launch {
            imageDataRepository.incrementDownloadCount(imageId)
        }
    }

    /**
     * Get images by category
     */
    fun getImagesByCategory(category: String, limit: Int = 50) {
        viewModelScope.launch {
            imageDataRepository.getImagesByCategory(category, limit).fold(
                onSuccess = { images ->
                    _uiState.update { 
                        it.copy(categoryImages = images)
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(error = error.message)
                    }
                }
            )
        }
    }

    /**
     * Search images
     */
    fun searchImages(query: String, limit: Int = 50) {
        viewModelScope.launch {
            _uiState.update { it.copy(isSearching = true) }
            
            imageDataRepository.searchImages(query, limit).fold(
                onSuccess = { images ->
                    _uiState.update { 
                        it.copy(
                            searchResults = images,
                            isSearching = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isSearching = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Select/deselect image for batch operations
     */
    fun toggleImageSelection(imageId: String) {
        val currentSelection = _uiState.value.selectedImages
        val updatedSelection = if (currentSelection.contains(imageId)) {
            currentSelection - imageId
        } else {
            currentSelection + imageId
        }
        _uiState.update { it.copy(selectedImages = updatedSelection) }
    }

    /**
     * Select all visible images
     */
    fun selectAllImages(imageIds: List<String>) {
        _uiState.update { it.copy(selectedImages = imageIds.toSet()) }
    }

    /**
     * Clear selection
     */
    fun clearSelection() {
        _uiState.update { it.copy(selectedImages = emptySet()) }
    }

    /**
     * Toggle selection mode
     */
    fun toggleSelectionMode() {
        val currentMode = _uiState.value.isSelectionMode
        _uiState.update { 
            it.copy(
                isSelectionMode = !currentMode,
                selectedImages = if (!currentMode) emptySet() else it.selectedImages
            )
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * Clear deleted image notification
     */
    fun clearDeletedNotification() {
        _uiState.update { 
            it.copy(
                deletedImageId = null,
                deletedCount = null
            )
        }
    }
}

/**
 * UI State for Image List screen
 */
data class ImageListUiState(
    val categories: List<Category> = emptyList(),
    val imageStats: ImageStats? = null,
    val categoryImages: List<ImageData> = emptyList(),
    val searchResults: List<ImageData> = emptyList(),
    val selectedImages: Set<String> = emptySet(),
    val isSelectionMode: Boolean = false,
    val isLoadingStats: Boolean = false,
    val isSearching: Boolean = false,
    val isDeleting: Boolean = false,
    val deletedImageId: String? = null,
    val deletedCount: Int? = null,
    val error: String? = null
)
