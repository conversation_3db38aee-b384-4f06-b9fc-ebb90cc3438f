package com.tfkcolin.joceladmin.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.Product
import com.tfkcolin.joceladmin.data.models.ProductCreateRequest
import com.tfkcolin.joceladmin.data.models.ProductUpdateRequest
import com.tfkcolin.joceladmin.data.models.ProductDimensions
import com.tfkcolin.joceladmin.data.models.ProductSEO
import com.tfkcolin.joceladmin.repository.CategoryRepository
import com.tfkcolin.joceladmin.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for product details (create/edit)
 */
@HiltViewModel
class ProductDetailsViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProductDetailsUiState())
    val uiState: StateFlow<ProductDetailsUiState> = _uiState.asStateFlow()

    private var currentProductId: String? = null

    init {
        loadCategories()
    }

    /**
     * Load product for editing
     */
    fun loadProduct(productId: String) {
        currentProductId = productId
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            productRepository.getProductById(productId)
                .onSuccess { product ->
                    product?.let { populateFromProduct(it) }
                    _uiState.update { it.copy(isLoading = false) }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            error = error.message,
                            isLoading = false
                        )
                    }
                }
        }
    }

    /**
     * Load categories for dropdown
     */
    private fun loadCategories() {
        viewModelScope.launch {
            categoryRepository.getRootCategories()
                .onSuccess { categories ->
                    _uiState.update { it.copy(categories = categories) }
                }
                .onFailure { error ->
                    _uiState.update { it.copy(error = error.message) }
                }
        }
    }

    /**
     * Populate form from existing product
     */
    private fun populateFromProduct(product: Product) {
        _uiState.update { state ->
            state.copy(
                name = product.name,
                description = product.description,
                sku = product.sku,
                category = product.category,
                genre = product.genre,
                brand = product.brand,
                price = product.price.toString(),
                cost = product.cost.toString(),
                currency = product.currency,
                stock = product.stock.toString(),
                minStock = product.minStock.toString(),
                isActive = product.isActive,
                isAvailable = product.isAvailable,
                isFeatured = product.isFeatured,
                images = product.images
            )
        }
        validateForm()
    }

    /**
     * Update product name
     */
    fun updateName(name: String) {
        _uiState.update { it.copy(name = name, nameError = null) }
        validateForm()
    }

    /**
     * Update product description
     */
    fun updateDescription(description: String) {
        _uiState.update { it.copy(description = description) }
    }

    /**
     * Update product SKU
     */
    fun updateSku(sku: String) {
        _uiState.update { it.copy(sku = sku) }
    }

    /**
     * Update product category
     */
    fun updateCategory(category: String?) {
        _uiState.update { it.copy(category = category ?: "", categoryError = null) }
        validateForm()
    }

    /**
     * Update product genre
     */
    fun updateGenre(genre: String) {
        _uiState.update { it.copy(genre = genre) }
    }

    /**
     * Update product brand
     */
    fun updateBrand(brand: String) {
        _uiState.update { it.copy(brand = brand) }
    }

    /**
     * Update product price
     */
    fun updatePrice(price: String) {
        _uiState.update { it.copy(price = price, priceError = null) }
        validateForm()
    }

    /**
     * Update product cost
     */
    fun updateCost(cost: String) {
        _uiState.update { it.copy(cost = cost) }
    }

    /**
     * Update product currency
     */
    fun updateCurrency(currency: String) {
        _uiState.update { it.copy(currency = currency) }
    }

    /**
     * Update product stock
     */
    fun updateStock(stock: String) {
        _uiState.update { it.copy(stock = stock) }
    }

    /**
     * Update product minimum stock
     */
    fun updateMinStock(minStock: String) {
        _uiState.update { it.copy(minStock = minStock) }
    }

    /**
     * Update product active status
     */
    fun updateIsActive(isActive: Boolean) {
        _uiState.update { it.copy(isActive = isActive) }
    }

    /**
     * Update product available status
     */
    fun updateIsAvailable(isAvailable: Boolean) {
        _uiState.update { it.copy(isAvailable = isAvailable) }
    }

    /**
     * Update product featured status
     */
    fun updateIsFeatured(isFeatured: Boolean) {
        _uiState.update { it.copy(isFeatured = isFeatured) }
    }

    /**
     * Validate form
     */
    private fun validateForm() {
        val state = _uiState.value
        var isValid = true
        var nameError: String? = null
        var categoryError: String? = null
        var priceError: String? = null

        // Validate name
        if (state.name.isBlank()) {
            nameError = "Product name is required"
            isValid = false
        }

        // Validate category
        if (state.category.isBlank()) {
            categoryError = "Category is required"
            isValid = false
        }

        // Validate price
        val price = state.price.toDoubleOrNull()
        if (price == null || price < 0) {
            priceError = "Valid price is required"
            isValid = false
        }

        _uiState.update { 
            it.copy(
                isValid = isValid,
                nameError = nameError,
                categoryError = categoryError,
                priceError = priceError
            )
        }
    }

    /**
     * Save product
     */
    fun saveProduct() {
        if (!_uiState.value.isValid) return

        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val state = _uiState.value
                val request = ProductCreateRequest(
                    name = state.name,
                    description = state.description,
                    sku = state.sku,
                    category = state.category,
                    genre = state.genre,
                    brand = state.brand,
                    price = state.price.toDoubleOrNull() ?: 0.0,
                    cost = state.cost.toDoubleOrNull() ?: 0.0,
                    currency = state.currency.ifEmpty { "USD" },
                    stock = state.stock.toIntOrNull() ?: 0,
                    minStock = state.minStock.toIntOrNull() ?: 0,
                    dimensions = ProductDimensions(),
                    seoData = ProductSEO()
                )

                if (currentProductId != null) {
                    // Update existing product - convert to update request
                    val updateRequest = ProductUpdateRequest(
                        id = currentProductId!!,
                        name = request.name,
                        description = request.description,
                        sku = request.sku,
                        category = request.category,
                        genre = request.genre,
                        brand = request.brand,
                        supplier = request.supplier,
                        price = request.price,
                        cost = request.cost,
                        currency = request.currency,
                        weight = request.weight,
                        dimensions = request.dimensions,
                        stock = request.stock,
                        minStock = request.minStock,
                        specifications = request.specifications,
                        seoData = request.seoData
                    )
                    productRepository.updateProduct(updateRequest)
                } else {
                    // Create new product
                    productRepository.createProduct(request)
                }

                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        isSaved = true
                    )
                }
            } catch (error: Exception) {
                _uiState.update { 
                    it.copy(
                        error = error.message,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Delete product
     */
    fun deleteProduct() {
        currentProductId?.let { productId ->
            viewModelScope.launch {
                _uiState.update { it.copy(isLoading = true) }
                
                productRepository.deleteProduct(productId)
                    .onSuccess {
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                isSaved = true
                            )
                        }
                    }
                    .onFailure { error ->
                        _uiState.update { 
                            it.copy(
                                error = error.message,
                                isLoading = false
                            )
                        }
                    }
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
}

/**
 * UI state for product details screen
 */
data class ProductDetailsUiState(
    val name: String = "",
    val description: String = "",
    val sku: String = "",
    val category: String = "",
    val genre: String = "",
    val brand: String = "",
    val price: String = "",
    val cost: String = "",
    val currency: String = "USD",
    val stock: String = "",
    val minStock: String = "",
    val isActive: Boolean = true,
    val isAvailable: Boolean = true,
    val isFeatured: Boolean = false,
    val images: List<String> = emptyList(),
    val categories: List<Category> = emptyList(),
    val isValid: Boolean = false,
    val isLoading: Boolean = false,
    val isSaved: Boolean = false,
    val error: String? = null,
    val nameError: String? = null,
    val categoryError: String? = null,
    val priceError: String? = null
)
