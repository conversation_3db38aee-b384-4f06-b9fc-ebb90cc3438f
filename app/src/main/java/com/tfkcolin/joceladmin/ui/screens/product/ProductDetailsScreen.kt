package com.tfkcolin.joceladmin.ui.screens.product

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.joceladmin.ui.components.common.JACTopAppBar
import com.tfkcolin.joceladmin.ui.viewmodels.ProductDetailsViewModel

/**
 * Product details screen for creating/editing products
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductDetailsScreen(
    productId: String,
    onNavigateBack: () -> Unit,
    onNavigateToImageGallery: () -> Unit = {},
    viewModel: ProductDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val isNewProduct = productId.isEmpty()
    
    // Initialize with product ID
    LaunchedEffect(productId) {
        if (productId.isNotEmpty()) {
            viewModel.loadProduct(productId)
        }
    }
    
    // Handle save success
    LaunchedEffect(uiState.isSaved) {
        if (uiState.isSaved) {
            onNavigateBack()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        JACTopAppBar(
            title = if (isNewProduct) "Add Product" else "Edit Product",
            onNavigationClick = onNavigateBack,
            actions = {
                if (!isNewProduct) {
                    IconButton(
                        onClick = { viewModel.deleteProduct() }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete product"
                        )
                    }
                }
                
                TextButton(
                    onClick = { viewModel.saveProduct() },
                    enabled = uiState.isValid && !uiState.isLoading
                ) {
                    Text("Save")
                }
            }
        )

        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Basic Information
                item {
                    Card {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Basic Information",
                                style = MaterialTheme.typography.titleMedium
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // Product Name
                            OutlinedTextField(
                                value = uiState.name,
                                onValueChange = viewModel::updateName,
                                label = { Text("Product Name *") },
                                modifier = Modifier.fillMaxWidth(),
                                isError = uiState.nameError != null,
                                supportingText = uiState.nameError?.let { { Text(it) } }
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // Description
                            OutlinedTextField(
                                value = uiState.description,
                                onValueChange = viewModel::updateDescription,
                                label = { Text("Description") },
                                modifier = Modifier.fillMaxWidth(),
                                minLines = 3,
                                maxLines = 5
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // SKU
                            OutlinedTextField(
                                value = uiState.sku,
                                onValueChange = viewModel::updateSku,
                                label = { Text("SKU") },
                                modifier = Modifier.fillMaxWidth(),
                                placeholder = { Text("Auto-generated if empty") }
                            )
                        }
                    }
                }

                // Category and Classification
                item {
                    Card {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Category & Classification",
                                style = MaterialTheme.typography.titleMedium
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // Category Dropdown
                            CategoryDropdown(
                                selectedCategory = uiState.category,
                                categories = uiState.categories,
                                onCategorySelect = viewModel::updateCategory
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // Genre
                            OutlinedTextField(
                                value = uiState.genre,
                                onValueChange = viewModel::updateGenre,
                                label = { Text("Genre") },
                                modifier = Modifier.fillMaxWidth()
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // Brand
                            OutlinedTextField(
                                value = uiState.brand,
                                onValueChange = viewModel::updateBrand,
                                label = { Text("Brand") },
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }

                // Pricing and Inventory
                item {
                    Card {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Pricing & Inventory",
                                style = MaterialTheme.typography.titleMedium
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                // Price
                                OutlinedTextField(
                                    value = uiState.price,
                                    onValueChange = viewModel::updatePrice,
                                    label = { Text("Price *") },
                                    modifier = Modifier.weight(1f),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    isError = uiState.priceError != null,
                                    supportingText = uiState.priceError?.let { { Text(it) } }
                                )
                                
                                // Cost
                                OutlinedTextField(
                                    value = uiState.cost,
                                    onValueChange = viewModel::updateCost,
                                    label = { Text("Cost") },
                                    modifier = Modifier.weight(1f),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // Currency
                            OutlinedTextField(
                                value = uiState.currency,
                                onValueChange = viewModel::updateCurrency,
                                label = { Text("Currency") },
                                modifier = Modifier.fillMaxWidth(),
                                placeholder = { Text("USD") }
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                // Stock
                                OutlinedTextField(
                                    value = uiState.stock,
                                    onValueChange = viewModel::updateStock,
                                    label = { Text("Stock") },
                                    modifier = Modifier.weight(1f),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                                )
                                
                                // Min Stock
                                OutlinedTextField(
                                    value = uiState.minStock,
                                    onValueChange = viewModel::updateMinStock,
                                    label = { Text("Min Stock") },
                                    modifier = Modifier.weight(1f),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                                )
                            }
                        }
                    }
                }

                // Product Status
                item {
                    Card {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Product Status",
                                style = MaterialTheme.typography.titleMedium
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // Active Status
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("Active")
                                Switch(
                                    checked = uiState.isActive,
                                    onCheckedChange = viewModel::updateIsActive
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            // Available Status
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("Available")
                                Switch(
                                    checked = uiState.isAvailable,
                                    onCheckedChange = viewModel::updateIsAvailable
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            // Featured Status
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("Featured")
                                Switch(
                                    checked = uiState.isFeatured,
                                    onCheckedChange = viewModel::updateIsFeatured
                                )
                            }
                        }
                    }
                }

                // Images Section
                item {
                    Card(
                        onClick = onNavigateToImageGallery
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column {
                                Text(
                                    text = "Product Images",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Text(
                                    text = "${uiState.images.size} images",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                            
                            Icon(
                                imageVector = Icons.Default.ChevronRight,
                                contentDescription = "Manage images"
                            )
                        }
                    }
                }
            }
        }
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show error snackbar
            viewModel.clearError()
        }
    }
}
