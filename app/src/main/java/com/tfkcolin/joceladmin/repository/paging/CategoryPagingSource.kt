package com.tfkcolin.joceladmin.repository.paging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.QuerySnapshot
import com.tfkcolin.joceladmin.data.models.Category
import com.tfkcolin.joceladmin.data.models.CategorySearchFilters
import com.tfkcolin.joceladmin.data.models.CategorySortBy
import com.tfkcolin.joceladmin.data.models.SortOrder
import kotlinx.coroutines.tasks.await

/**
 * Paging source for categories with filtering support
 */
class CategoryPagingSource(
    private val firestore: FirebaseFirestore,
    private val filters: CategorySearchFilters
) : PagingSource<DocumentSnapshot, Category>() {

    companion object {
        private const val COLLECTION_CATEGORIES = "categories"
    }

    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, Category> {
        return try {
            val pageSize = params.loadSize
            var query: Query = firestore.collection(COLLECTION_CATEGORIES)

            // Apply filters
            filters.parentId?.let { parentId ->
                query = query.whereEqualTo("parentId", parentId)
            }

            filters.level?.let { level ->
                query = query.whereEqualTo("level", level)
            }

            filters.isActive?.let { isActive ->
                query = query.whereEqualTo("isActive", isActive)
            }

            filters.isVisible?.let { isVisible ->
                query = query.whereEqualTo("isVisible", isVisible)
            }

            filters.hasProducts?.let { hasProducts ->
                if (hasProducts) {
                    query = query.whereGreaterThan("productCount", 0)
                } else {
                    query = query.whereEqualTo("productCount", 0)
                }
            }

            filters.hasImages?.let { hasImages ->
                if (hasImages) {
                    query = query.whereGreaterThan("imageCount", 0)
                } else {
                    query = query.whereEqualTo("imageCount", 0)
                }
            }

            // Apply sorting
            val sortField = when (filters.sortBy) {
                CategorySortBy.NAME -> "name"
                CategorySortBy.SORT_ORDER -> "sortOrder"
                CategorySortBy.PRODUCT_COUNT -> "productCount"
                CategorySortBy.IMAGE_COUNT -> "imageCount"
                CategorySortBy.CREATED_DATE -> "createdAt"
                CategorySortBy.UPDATED_DATE -> "updatedAt"
            }

            val sortDirection = when (filters.sortOrder) {
                SortOrder.ASC -> Query.Direction.ASCENDING
                SortOrder.DESC -> Query.Direction.DESCENDING
            }

            query = query.orderBy(sortField, sortDirection)

            // Apply pagination
            params.key?.let { lastDocument ->
                query = query.startAfter(lastDocument)
            }

            query = query.limit(pageSize.toLong())

            // Execute query
            val querySnapshot: QuerySnapshot = query.get().await()
            val categories = querySnapshot.toObjects(Category::class.java)

            // Filter by search query if provided (client-side filtering for text search)
            val filteredCategories = if (filters.searchQuery.isNotEmpty()) {
                categories.filter { category ->
                    filters.searchQuery.lowercase() in category.name.lowercase() ||
                    filters.searchQuery.lowercase() in category.description.lowercase() ||
                    filters.searchQuery.lowercase() in category.slug.lowercase()
                }
            } else {
                categories
            }

            // Determine next key
            val nextKey = if (querySnapshot.documents.isNotEmpty() && filteredCategories.size == pageSize) {
                querySnapshot.documents.lastOrNull()
            } else {
                null
            }

            LoadResult.Page(
                data = filteredCategories,
                prevKey = null, // Only support forward pagination
                nextKey = nextKey
            )

        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }

    override fun getRefreshKey(state: PagingState<DocumentSnapshot, Category>): DocumentSnapshot? {
        // Return null to always start from the beginning on refresh
        return null
    }
}
